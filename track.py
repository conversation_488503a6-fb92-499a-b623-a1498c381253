import pyJianYingDraft as draft


def track_exists(script: draft.ScriptFile, track_name: str) -> bool:
    """ Check if the track exists in the script """
    
    return track_name in script.tracks

def get_or_create_track(script: draft.ScriptFile, track_type: draft.TrackType, track_name: str) -> dict:
    """ Get track or create new track"""

    if track_exists(script, track_name):
        # print(f"Track '{track_name}' already exists")
        return script.tracks[track_name]
    else:
        print(f"Creating new track '{track_name}'")
        new_track = script.add_track(track_type, track_name)
        return new_track