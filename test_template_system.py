#!/usr/bin/env python
"""
Test script to verify the complete template system workflow
Tests: config.toml + paragraph.json → draft file
"""

import os
import sys
from pathlib import Path
from template_manager import TemplateManager
from config import BASE_DRAFT_FOLDER


def test_complete_workflow():
    """Test the complete workflow from config.toml and paragraph.json to draft file"""
    
    # Setup paths
    base_draft_path = BASE_DRAFT_FOLDER  # Test output directory
    template_path = "config.toml"  # Use simplified test config
    paragraph_json_path = "paragraph.json"
    
    # Check if required files exist
    if not os.path.exists(template_path):
        print(f"Error: Template file '{template_path}' not found")
        return False
    
    if not os.path.exists(paragraph_json_path):
        print(f"Error: Paragraph JSON file '{paragraph_json_path}' not found")
        return False
    
    # Create test draft directory if it doesn't exist
    Path(base_draft_path).mkdir(exist_ok=True)
    
    # Prepare test materials
    # Note: These are placeholder paths, actual files may not exist
    video_paths = [r"C:/All/T_files/test/一个有钱有资源.mp4"]
    image_paths = [r"C:/All/T_files/test/图片合集/black.png"]  # Using the image from config.toml
    
    try:
        # Initialize template manager
        print("=" * 60)
        print("Starting Template System Test")
        print("=" * 60)
        
        manager = TemplateManager(base_draft_path)
        
        # Apply template to create new draft with unique name
        import time
        draft_name = f"test_template_draft_{int(time.time())}"
        draft_path = manager.apply_template(
            draft_name=draft_name,
            template_path=template_path,
            paragraph_json_path=paragraph_json_path,
            video_paths=video_paths,
            image_paths=image_paths,
            canvas_width=1080,
            canvas_height=1920
        )
        
        print("=" * 60)
        print("[SUCCESS] Test completed successfully!")
        print(f"[SUCCESS] Draft saved at: {draft_path}")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print("=" * 60)
        print(f"[ERROR] Test failed with error: {e}")
        print("=" * 60)
        import traceback
        traceback.print_exc()
        return False


def check_dependencies():
    """Check if all required dependencies are available"""
    print("Checking dependencies...")
    
    try:
        import pyJianYingDraft
        print("[OK] pyJianYingDraft is installed")
    except ImportError:
        print("[ERROR] pyJianYingDraft is not installed")
        return False
    
    try:
        import toml
        print("[OK] toml is installed")
    except ImportError:
        print("[ERROR] toml is not installed")
        return False
    
    return True


if __name__ == "__main__":
    print("Template System End-to-End Test")
    print("-" * 60)
    
    # Check dependencies first
    if not check_dependencies():
        print("Please install missing dependencies with: uv add <package>")
        sys.exit(1)
    
    print()
    
    # Run the test
    success = test_complete_workflow()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)