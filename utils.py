# Time conversion constants
MICROSECONDS_PER_SECOND = 1_000_000
MICROSECONDS_PER_FRAME_30FPS = 33_333  # ~33.333ms per frame at 30fps


def align_to_frame_boundary(microseconds, fps=30):
    """
    将微秒时间对齐到最近的帧边界
    fps: 帧率，默认30
    """
    microseconds_per_frame = 1000000 / fps
    frame_number = round(microseconds / microseconds_per_frame)
    return int(frame_number * microseconds_per_frame)


def frames_to_microseconds(frames: int, fps: int = 30) -> int:
    """
    Convert frame count to microseconds.
    
    Args:
        frames: Number of frames
        fps: Frames per second (default 30)
        
    Returns:
        Time in microseconds
    """
    frame_duration_us = MICROSECONDS_PER_SECOND // fps
    return frames * frame_duration_us


def microseconds_to_frames(microseconds: int, fps: int = 30) -> int:
    """
    Convert microseconds to frame count.
    
    Args:
        microseconds: Time in microseconds
        fps: Frames per second (default 30)
        
    Returns:
        Number of frames (rounded)
    """
    frame_duration_us = MICROSECONDS_PER_SECOND // fps
    return round(microseconds / frame_duration_us)


def seconds_to_microseconds(seconds: float) -> int:
    """
    Convert seconds to microseconds.
    
    Args:
        seconds: Time in seconds
        
    Returns:
        Time in microseconds
    """
    return int(seconds * MICROSECONDS_PER_SECOND)


def microseconds_to_seconds(microseconds: int) -> float:
    """
    Convert microseconds to seconds.
    
    Args:
        microseconds: Time in microseconds
        
    Returns:
        Time in seconds
    """
    return microseconds / MICROSECONDS_PER_SECOND


def hex_to_rgb(hex_color):
    """
    将十六进制颜色字符串转换为float类型的RGB三元组（0~1）。
    始终返回float类型，确保兼容要求 (Tuple[float, float, float])。
    严格返回3个元素的tuple。
    """
    hex_color = hex_color.lstrip("#")
    # 补全为6位
    if len(hex_color) == 3:
        hex_color = "".join([c * 2 for c in hex_color])
    if len(hex_color) != 6:
        raise ValueError("颜色字符串长度必须为6位")
    r = int(hex_color[0:2], 16)
    g = int(hex_color[2:4], 16)
    b = int(hex_color[4:6], 16)
    return (round(r / 255, 4), round(g / 255, 4), round(b / 255, 4))


def px_to_position(px: int, width: int = 1920) -> float:
    """
    Convert a pixel value to a relative position by dividing by the given width.
    :param px: The pixel value to convert. Can be positive or negative.
    :param width: The reference width for normalization. Default is 1920.
    :return: The normalized position, equal to px divided by width, rounded to 5 decimal places.
    >>>px_to_position(-600)
    -0.3125
    >>>px_to_position(960)
    0.5
    >>>px_to_position(1, 3)
    0.33333
    """
    return round(px / width, 5)

def line_break(text: str, break_keywords: list[str], delete_keywords: list[str]) -> str:
    """
    根据关键词，将文本进行换行和删除处理。
    break_keywords: 需要换行并删除的关键词列表
    delete_keywords: 只需要删除不换行的关键词列表
    """
    result = ""
    i = 0

    while i < len(text):
        char = text[i]
        
        # 检查换行关键词
        break_keyword_match = None
        for keyword in break_keywords:
            if text[i:i+len(keyword)] == keyword:
                break_keyword_match = keyword
                break
        
        # 检查删除关键词
        delete_keyword_match = None
        if not break_keyword_match:
            for keyword in delete_keywords:
                if text[i:i+len(keyword)] == keyword:
                    delete_keyword_match = keyword
                    break
        
        if break_keyword_match:
            result += "\n"
            i += len(break_keyword_match)
        elif delete_keyword_match:
            i += len(delete_keyword_match)
        else:
            result += char
            i += 1

    # 删除空行
    lines = result.split("\n")
    non_empty_lines = [line for line in lines if line.strip()]

    # 检查每行字数，如果大于16个字，在行开头标注
    final_lines = []
    for line in non_empty_lines:
        if len(line) > 16:
            final_lines.append("<大于16个字>" + line)
        else:
            final_lines.append(line)

    return "\n".join(final_lines)


# [CUT] 提示词的处理
def extract_cut_lines(text: str) -> list[dict]:
    """
    读取文件并提取[CUT]标记前的行，构造cut response对象
    返回格式: [{"text": "前一句内容", "should_cut_after": true}, ...]
    """
    import re
    
    # 先删除除了[CUT]以外的所有[]标记（包括[]中的内容）
    # 匹配所有[xxx]格式，但保留[CUT]
    cleaned_text = re.sub(r'\[(?!CUT\])[^\]]*\]', '', text)
    
    lines = cleaned_text.split("\n")
    cut_responses = []

    for i, line in enumerate(lines):
        line = line.strip()
        
        # 如果当前行是[CUT]标记，获取上一个非空行
        if line == "[CUT]" and i > 0:
            # 从前一行开始向前查找，跳过所有空行
            j = i - 1
            while j >= 0 and lines[j].strip() == "":
                j -= 1
            
            # 找到非空行后添加到结果中
            if j >= 0:
                previous_line = lines[j].strip()
                if previous_line:  # 确保找到的行有内容
                    cut_responses.append(
                        {"text": previous_line, "should_cut_after": True}
                    )

    return cut_responses


# 剪辑点应用到最后文案json
def merge_cut_data_into_article(
    article_json: list[dict], cut_data: list[dict]
) -> list[dict]:
    """
    将extract_cut_lines的返回值根据"text"字段匹配，合并到article json中

    Args:
        article_json: 包含文章片段的json数组，每个元素包含"text"字段
        cut_data: extract_cut_lines返回的数据，格式为[{"text": "内容", "should_cut_after": true}, ...]

    Returns:
        合并后的article json，在匹配的元素中添加"should_cut_after"字段
    """
    # 创建cut_data的文本映射表，便于快速查找
    cut_text_map = {item["text"]: item["should_cut_after"] for item in cut_data}

    # 遍历article_json，为匹配的文本添加should_cut_after字段
    result = []
    for article_item in article_json:
        # 复制原始数据
        new_item = article_item.copy()

        # 检查是否在cut_data中有匹配的文本
        if article_item["text"] in cut_text_map:
            new_item["should_cut_after"] = cut_text_map[article_item["text"]]
        else:
            # 如果没有匹配，默认设置为False
            new_item["should_cut_after"] = False

        result.append(new_item)

    return result

def get_audio_volume(dB: int) -> float:
    """
    将分贝值（dB）转换为线性音量（float）。

    Args:
        dB (int): 分贝值。

    Returns:
        float: 线性音量值。
    """
    return 10 ** (dB / 20)



if __name__ == "__main__":

    # 调用换行处理函数，处理test.txt并保存到test1.txt
    with open("test.txt", "r", encoding="utf-8") as file:
        text = file.read()
    article = line_break(text, break_keywords=["。", "，", "！", "：", "？", "?", ","], delete_keywords=["“", "”"])
    with open("test1.txt", "w", encoding="utf-8") as file:
        file.write(article)