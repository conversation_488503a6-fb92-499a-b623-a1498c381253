import toml

# Load raw config
config = toml.load('config.toml')
track_data = config['paragraph'][0]['track'][0]

print("Raw track data from TOML:")
print(f"  type: {track_data.get('type')}")
print(f"  effect: {track_data.get('effect')}")
print(f"  effect repr: {repr(track_data.get('effect'))}")
print(f"  params: {track_data.get('params')}")

# Now load through template system
from template_loader import TemplateLoader
loader = TemplateLoader()
template = loader.load_template('config.toml')

first_track = template.paragraphs[0].tracks[0]
print("\nLoaded track through template system:")
print(f"  type: {first_track.type}")
print(f"  effect: {first_track.effect}")
print(f"  effect repr: {repr(first_track.effect)}")
print(f"  params: {first_track.params}")