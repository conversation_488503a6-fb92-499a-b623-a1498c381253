{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(uv:*)", "Bash(find:*)", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "Bash(npm:*)", "Bash(ls:*)", "Bash(cp:*)", "Write", "Edit", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(touch:*)", "Bash(rm:*)"], "deny": []}, "hooks": {"SessionStart": [{"matcher": "clear", "hooks": [{"type": "command", "command": "uv run claude/hooks/session_start/git_check.py"}, {"type": "command", "command": "uv run claude/hooks/session_start/load_source.py"}, {"type": "command", "command": "uv run claude/hooks/session_start/log_session_info.py"}]}], "UserPromptSubmit": [{"hooks": [{"type": "command", "command": "uv run claude/hooks/user_prompt_submit/log_info.py"}]}], "Stop": [{"hooks": [{"type": "command", "command": "uv run claude/hooks/stop/ruff_check.py"}]}], "PostToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run claude/hooks/post_tool_use.py"}]}]}, "outputStyle": "Learning"}