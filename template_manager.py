import json
import py<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as draft
from pathlib import Path
from template_loader import Template<PERSON>oader
from template_processor import TemplateProcessor
from template import VideoTemplate
import config


class TemplateManager:
    """Main class for managing and applying video templates"""
    
    def __init__(self, base_draft_path: str, canvas_width: int = 1080, canvas_height: int = 1920):
        """
        Initialize TemplateManager
        
        Args:
            base_draft_path: Base path for JianYing drafts (default from config.BASE_DRAFT_FOLDER)
            canvas_width: Canvas width in pixels (default from config.DEFAULT_CANVAS_WIDTH)
            canvas_height: Canvas height in pixels (default from config.DEFAULT_CANVAS_HEIGHT)
        """
        # Use config defaults if not provided
        self.base_draft_path: str = base_draft_path or config.BASE_DRAFT_FOLDER
        self.canvas_width: int = canvas_width or config.DEFAULT_CANVAS_WIDTH
        self.canvas_height: int = canvas_height or config.DEFAULT_CANVAS_HEIGHT
        
        self.loader: TemplateLoader = TemplateLoader()
        self.processor: TemplateProcessor = TemplateProcessor(self.canvas_width, self.canvas_height)
        
        # Initialize DraftFolder with the configured path
        # Note: DraftFolder expects the directory to exist
        self.draft_folder: draft.DraftFolder = draft.DraftFolder(self.base_draft_path)
    
    def load_template_from_file(self, template_path: str) -> VideoTemplate:
        """
        Load template from TOML file
        
        Args:
            template_path: Path to TOML template file
        
        Returns:
            Loaded VideoTemplate object
        """
        template = self.loader.load_template(template_path)
        is_valid = self.loader.validate_template(template)
        if is_valid:
            print("Template loaded successfully.")
        else:
            print("Failed to load template.")
        return template
    
    def load_paragraph_data(self, json_path: str) -> list[dict[str, any]]:
        """
        Load paragraph timing data from JSON file
        
        Args:
            json_path: Path to paragraph JSON file
        
        Returns:
            List of paragraph data dictionaries
        """
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Ensure it's a list
        if not isinstance(data, list):
            data = [data]
        
        return data
    
    def prepare_materials(self, template: VideoTemplate, 
                         video_paths: list[str] | None = None,
                         image_paths: list[str] | None = None) -> dict[str, any]:
        """
        Prepare materials based on template requirements
        
        Args:
            template: The video template
            video_paths: List of video file paths
            image_paths: List of image file paths
        
        Returns:
            Dictionary of prepared materials
        """
        materials = {}
        
        # Check template meta for material requirements
        meta = template.meta
        
        # Prepare videos
        if meta.get('videos', 0) > 0:
            if video_paths:
                materials['videos'] = video_paths[:meta['videos']]
            else:
                print(f"Warning: Template requires {meta['videos']} videos but none provided")
                materials['videos'] = []
        
        # Prepare images
        if meta.get('images', 0) > 0:
            if image_paths:
                materials['images'] = image_paths[:meta['images']]
            else:
                print(f"Warning: Template requires {meta['images']} images but none provided")
                materials['images'] = []
        
        return materials
    
    def apply_template(self, 
                      draft_name: str,
                      template_path: str,
                      paragraph_json_path: str,
                      video_paths: list[str] | None = None,
                      image_paths: list[str] | None = None,
                      canvas_width: int | None = None,
                      canvas_height: int | None = None) -> str:
        """
        Apply template to create a new draft
        
        Args:
            draft_name: Name for the new draft
            template_path: Path to TOML template file
            paragraph_json_path: Path to paragraph timing JSON
            video_paths: List of video file paths
            image_paths: List of image file paths
            canvas_width: Canvas width in pixels (if None, uses template meta or default 1080)
            canvas_height: Canvas height in pixels (if None, uses template meta or default 1920)
        
        Returns:
            Path to created draft
        """
        try:
            # Load template
            print(f"Loading template from {template_path}...")
            template = self.load_template_from_file(template_path)
            
            # Get canvas dimensions from template meta or use provided/default values
            if canvas_width is None:
                canvas_width = template.meta.get('canvas_width', 1080)
            if canvas_height is None:
                canvas_height = template.meta.get('canvas_height', 1920)
            
            # Update processor with correct canvas dimensions
            self.processor.canvas_width = canvas_width
            self.processor.canvas_height = canvas_height
            
            print(f"Canvas size: {canvas_width}x{canvas_height}")
            
            # Load paragraph data
            print(f"Loading paragraph data from {paragraph_json_path}...")
            paragraphs_data = self.load_paragraph_data(paragraph_json_path)
            
            # Prepare materials
            print("Preparing materials...")
            materials = self.prepare_materials(template, video_paths, image_paths)
            
            # Create new draft (use config setting for replace)
            print(f"Creating draft '{draft_name}'...")
            script = self.draft_folder.create_draft(
                draft_name, 
                canvas_width, 
                canvas_height, 
                allow_replace=config.ALLOW_REPLACE_DRAFT
            )
            
            # Apply template
            print("Applying template to draft...")
            self.processor.process_template(script, template, paragraphs_data, materials)
            
            # Save draft
            print("Saving draft...")
            script.save()
            
            draft_path = str(Path(self.base_draft_path) / draft_name)
            print(f"Draft created successfully at: {draft_path}")
            
            return draft_path
            
        except Exception as e:
            print(f"Error applying template: {e}")
            raise
    
    def apply_template_to_existing(self,
                                  script: any,
                                  template: VideoTemplate,
                                  paragraphs_data: list[dict[str, any]],
                                  materials: dict[str, any]) -> None:
        """
        Apply template to an existing script
        
        Args:
            script: Existing pyJianYingDraft script object
            template: The video template
            paragraphs_data: List of paragraph timing data
            materials: Available materials
        """
        self.processor.process_template(script, template, paragraphs_data, materials)


# Example usage function
def example_usage():
    """Example of how to use the TemplateManager"""
    # Initialize manager (will use config.BASE_DRAFT_FOLDER by default)
    manager = TemplateManager()
    
    # Apply template to create new draft
    draft_path = manager.apply_template(
        draft_name="My Video Project",
        template_path="config.toml",
        paragraph_json_path="paragraph.json",
        video_paths=["video1.mp4", "video2.mp4"],
        image_paths=["image1.png"],
        canvas_width=1080,
        canvas_height=1920
    )
    
    print(f"Created draft at: {draft_path}")


if __name__ == "__main__":
    # Run example if executed directly
    example_usage()