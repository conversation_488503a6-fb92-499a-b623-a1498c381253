"""
Paragraph data type definitions using Pydantic for validation and serialization.

This module defines the data structures for paragraph timing information used
in video template processing. Each paragraph consists of pre, middle, and tail
segments with associated timing and text content.
"""

from pathlib import Path
import json

from pydantic import BaseModel, Field, field_validator, model_validator
from pydantic_core.core_schema import ValidationInfo

from utils import MICROSECONDS_PER_SECOND


class SegmentData(BaseModel):
    """
    Represents a single segment of paragraph data.
    
    A segment contains text content and timing information in microseconds.
    Used for pre, middle, and tail segments in paragraph structures.
    """
    text: str
    start: int  # Start time in microseconds
    duration: int  # Duration in microseconds
    
    @field_validator('start', 'duration')
    @classmethod
    def validate_non_negative(cls, v: int, info: ValidationInfo) -> int:
        """Ensure time values are non-negative"""
        if v < 0:
            field_name = info.field_name
            raise ValueError(f'{field_name} must be non-negative, got {v}')
        return v
    
    @field_validator('duration')
    @classmethod
    def validate_duration_positive(cls, v: int) -> int:
        """Ensure duration is positive (greater than 0)"""
        if v <= 0:
            raise ValueError(f'duration must be positive, got {v}')
        return v
    
    @property
    def end_time(self) -> int:
        """Calculate the end time of this segment"""
        return self.start + self.duration
    
    @property
    def duration_seconds(self) -> float:
        """Get duration in seconds"""
        return self.duration / MICROSECONDS_PER_SECOND
    
    @property
    def start_seconds(self) -> float:
        """Get start time in seconds"""
        return self.start / MICROSECONDS_PER_SECOND
    
    def overlaps_with(self, other: 'SegmentData') -> bool:
        """Check if this segment overlaps with another segment"""
        return not (self.end_time <= other.start or other.end_time <= self.start)


class ParagraphData(BaseModel):
    """
    Represents a complete paragraph with pre, middle, and tail segments.
    
    A paragraph is a logical unit of content that can have:
    - pre: Optional opening segment
    - middle: List of main content segments (can be empty or multiple)
    - tail: Optional closing segment
    """
    pre: SegmentData | None = None
    middle: list[SegmentData] = Field(default_factory=list)
    tail: SegmentData | None = None
    
    @model_validator(mode='after')
    def validate_timing_sequence(self) -> 'ParagraphData':
        """Validate that segments are in chronological order and don't overlap"""
        segments: list[tuple[str, SegmentData]] = []
        
        # Collect all segments
        if self.pre:
            segments.append(('pre', self.pre))
        for i, seg in enumerate(self.middle):
            segments.append((f'middle[{i}]', seg))
        if self.tail:
            segments.append(('tail', self.tail))
        
        # Check for chronological order and no overlaps
        for i in range(len(segments) - 1):
            name1, seg1 = segments[i]
            name2, seg2 = segments[i + 1]
            
            if seg1.end_time > seg2.start:
                raise ValueError(
                    f"Segments are not in chronological order or overlap: {name1} ends at {seg1.end_time}μs but {name2} starts at {seg2.start}μs"
                )
        
        return self
    
    @property
    def total_duration(self) -> int:
        """Calculate total duration of the paragraph in microseconds"""
        if not self.has_content:
            return 0
        
        start_time = self.start_time
        end_time = self.end_time
        
        return end_time - start_time if end_time > start_time else 0
    
    @property
    def start_time(self) -> int:
        """Get the start time of the first segment"""
        if self.pre:
            return self.pre.start
        elif self.middle:
            return self.middle[0].start
        elif self.tail:
            return self.tail.start
        return 0
    
    @property
    def end_time(self) -> int:
        """Get the end time of the last segment"""
        if self.tail:
            return self.tail.end_time
        elif self.middle:
            return self.middle[-1].end_time
        elif self.pre:
            return self.pre.end_time
        return 0
    
    @property
    def has_content(self) -> bool:
        """Check if paragraph has any content"""
        return bool(self.pre or self.middle or self.tail)
    
    @property
    def segment_count(self) -> int:
        """Count total number of segments"""
        count = 0
        if self.pre:
            count += 1
        count += len(self.middle)
        if self.tail:
            count += 1
        return count
    
    def get_segment_at_time(self, time_us: int) -> SegmentData | None:
        """Get the segment that contains the given time point"""
        if self.pre and self.pre.start <= time_us < self.pre.end_time:
            return self.pre
        
        for seg in self.middle:
            if seg.start <= time_us < seg.end_time:
                return seg
        
        if self.tail and self.tail.start <= time_us < self.tail.end_time:
            return self.tail
        
        return None


# Helper functions

def load_paragraph_data_from_json(file_path: str | Path) -> list[ParagraphData]:
    """
    Load paragraph data directly from JSON file using Pydantic's built-in JSON parsing.

    This function handles both single paragraph objects and lists of paragraphs.
    It uses Pydantic's model_validate_json for type-safe parsing and validation.

    Args:
        file_path: Path to the JSON file

    Returns:
        List of ParagraphData instances

    Raises:
        FileNotFoundError: If the file doesn't exist
        ValueError: If JSON is invalid or has wrong structure
    """
    path = Path(file_path)
    if not path.exists():
        raise FileNotFoundError(f"File not found: {file_path}")

    # Read the JSON content as text
    with open(path, 'r', encoding='utf-8') as f:
        json_content = f.read()

    try:
        # First, check if the JSON represents a list or a single object
        # We need to parse once to determine the structure
        parsed_data = json.loads(json_content)

        if isinstance(parsed_data, list):
            # It's a list of paragraphs
            paragraphs: list[ParagraphData] = []
            for i, item in enumerate(parsed_data):
                try:
                    # Convert each item back to JSON and parse with Pydantic
                    item_json = json.dumps(item)
                    paragraph = ParagraphData.model_validate_json(item_json)
                    paragraphs.append(paragraph)
                except Exception as e:
                    raise ValueError(f"Invalid paragraph data at index {i}: {e}") from e
        else:
            # It's a single paragraph object
            paragraph = ParagraphData.model_validate_json(json_content)
            paragraphs = [paragraph]

    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON format: {e}") from e
    except Exception as e:
        raise ValueError(f"Failed to parse paragraph data: {e}") from e

    return paragraphs
