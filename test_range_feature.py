#!/usr/bin/env python
"""
Test script for the new range feature in track templates
"""

import json
from pathlib import Path
from template_loader import Template<PERSON>oader
from template_processor import TemplateProcessor

def test_range_parsing():
    """Test that range fields are correctly parsed from config.toml"""
    
    print("Testing range feature parsing...")
    
    # Load the template
    loader = TemplateLoader()
    template = loader.load_template("config.toml")
    
    # Check if range is parsed correctly
    for i, paragraph in enumerate(template.paragraphs):
        print(f"\n=== Paragraph {i} ===")
        for j, track in enumerate(paragraph.tracks):
            print(f"  Track {j} ({track.type}):")
            print(f"    Timing: {track.timing}")
            if track.range:
                print(f"    Range: {track.range}")
                print(f"      Start: {track.range.get('start')}")
                print(f"      End: {track.range.get('end')}")
            else:
                print(f"    Range: None (uses entire paragraph)")
            print()

def test_range_calculation():
    """Test the actual time calculation with range"""
    
    print("\nTesting range time calculation...")
    
    # Load paragraph data
    with open("paragraph.json", "r", encoding="utf-8") as f:
        paragraph_data = json.load(f)[0]
    
    # Create processor
    processor = TemplateProcessor()
    
    # Test cases
    test_cases = [
        {
            "name": "pre.start to middle.end",
            "range": {
                "start": {"type": "pre", "position": "start"},
                "end": {"type": "middle", "position": "end"}
            }
        },
        {
            "name": "middle.start to tail.end", 
            "range": {
                "start": {"type": "middle", "position": "start", "index": 0},
                "end": {"type": "tail", "position": "end"}
            }
        },
        {
            "name": "pre.end to tail.start",
            "range": {
                "start": {"type": "pre", "position": "end"},
                "end": {"type": "tail", "position": "start"}
            }
        }
    ]
    
    for test in test_cases:
        print(f"\n  Test: {test['name']}")
        start_time = processor.calculate_range_time(test['range']['start'], paragraph_data)
        end_time = processor.calculate_range_time(test['range']['end'], paragraph_data)
        duration = end_time - start_time
        
        print(f"    Start time: {start_time} μs ({start_time/1000000:.2f}s)")
        print(f"    End time: {end_time} μs ({end_time/1000000:.2f}s)")
        print(f"    Duration: {duration} μs ({duration/1000000:.2f}s)")
        
        if duration <= 0:
            print(f"    WARNING: Invalid range (duration <= 0)")

def main():
    """Run all tests"""
    
    print("=" * 60)
    print("Range Feature Test Suite")
    print("=" * 60)
    
    test_range_parsing()
    test_range_calculation()
    
    print("\n" + "=" * 60)
    print("Tests completed!")
    print("=" * 60)

if __name__ == "__main__":
    main()