import pyJianYingDraft as draft

# Create a complete test with effect
folder = draft.DraftFolder("C:\\All\\Tools\\JianyingPro Drafts")
script = folder.create_draft(
    "test_effect_final",
    width=1080,
    height=1920,
    allow_replace=True
)

# Add a video track
script.add_track(draft.TrackType.video, "video_0")

# Create video material
mat = draft.VideoMaterial("图片合集/black.png")

# Create video segment
seg = draft.VideoSegment(
    mat,
    source_timerange=draft.trange(0, 5000000),
    target_timerange=draft.trange(0, 5000000)
)

# Add effect to segment
print("Adding effect to segment...")
effect_type = draft.VideoSceneEffectType.模糊
seg.add_effect(effect_type, [0.5])
print(f"Segment has {len(seg.effects)} effects")

# Add segment to script
script.add_segment(seg, "video_0")

# Check materials
print(f"\nMaterials in script:")
print(f"  Videos: {len(script.materials.videos)}")
print(f"  Video effects: {len(script.materials.video_effects)}")
if script.materials.video_effects:
    for effect in script.materials.video_effects:
        print(f"    - {effect.name}")

# Save
import os
draft_path = os.path.join("C:\\All\\Tools\\JianyingPro Drafts", "test_effect_final", "draft_content.json")
os.makedirs(os.path.dirname(draft_path), exist_ok=True)
script.dump(draft_path)
print(f"\nDraft saved successfully!")
print(f"Location: {draft_path}")