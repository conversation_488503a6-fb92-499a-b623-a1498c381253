"""
Global configuration for the JianYing automation project
"""

# JianYing draft folder path
# Change this to your actual JianYing draft folder location
# Default path: r"D:\JianYingPro Draft" (for actual JianYing installation)
# Test path: "./test_drafts" (for testing without JianYing)
BASE_DRAFT_FOLDER = r"C:\All\Tools\JianyingPro Drafts"  # Using test directory for development

# Default canvas dimensions
DEFAULT_CANVAS_WIDTH = 1080
DEFAULT_CANVAS_HEIGHT = 1920

# Default frame rate
DEFAULT_FPS = 30

# File paths
DEFAULT_TEMPLATE_PATH = "config.toml"
DEFAULT_PARAGRAPH_JSON_PATH = "paragraph.json"

# Material paths
DEFAULT_VIDEO_FOLDER = "videos"
DEFAULT_IMAGE_FOLDER = "images"
DEFAULT_AUDIO_FOLDER = "audios"

# Processing settings
ALLOW_REPLACE_DRAFT = True  # Whether to allow replacing existing drafts
SKIP_MISSING_MATERIALS = True  # Whether to skip missing material files instead of failing