{"title": "Code Analysis Menu", "description": "Menu of available code analysis commands", "prompt": "This project has several code analysis commands available in the .claude/commands directory:\n\n1. /analyze_code - Perform deep code analysis\n2. /generate_knowledge_graph - Create a contextual knowledge graph of the codebase\n3. /optimize_code - Optimize and improve code quality\n4. /evaluate_code_quality - Analyze and rate code quality\n\nEach command has been converted to the proper JSON format required by Claude Code. You can use any of these commands by typing the slash command in the chat.\n\nWhich code analysis would you like to perform?", "completion_prompt": "<PERSON> now understands the available code analysis commands and is ready to help you select and use one.ultrathink"}