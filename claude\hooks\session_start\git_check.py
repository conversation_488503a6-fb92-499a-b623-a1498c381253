#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# ///

import json
import subprocess
import sys
from pathlib import Path


def get_uncommitted_files():
    """
    Get list of files with uncommitted changes.
    Returns empty list if not a git repo or on error.
    """
    try:
        result = subprocess.run(
            ["git", "status", "--porcelain"],
            capture_output=True,
            text=True,
            cwd=Path.cwd(),
            check=False,
        )
        
        if result.returncode != 0:
            return []
        
        lines = result.stdout.splitlines()
        files = []
        
        for line in lines:
            if not line.strip():
                continue
            
            # Skip untracked files (those starting with ??)
            if line.startswith("??"):
                continue
            
            # Format is "XY filename" where XY is status code
            if len(line) > 3:
                filename = line[3:].strip()
                if filename:
                    files.append(filename)
        
        return files
        
    except (OSError, subprocess.SubprocessError):
        return []


def main():
    try:
        # Read JSON input from stdin (for hook compatibility)
        _ = json.loads(sys.stdin.read())
        
        # Check for uncommitted changes
        uncommitted = get_uncommitted_files()
        
        if uncommitted:
            # Build error message
            message_parts = [
                "Uncommitted changes detected in the following files:",
                *uncommitted,
                "\nPlease commit or stash your changes before proceeding.",
            ]
            error_msg = "\n".join(message_parts)
            
            # Exit code 2 blocks the prompt with error message
            print(f"Prompt blocked: {error_msg}", file=sys.stderr)
            sys.exit(2)
        
        # All clear, let the prompt through
        sys.exit(0)
        
    except json.JSONDecodeError:
        # If we can't parse JSON, let it through
        sys.exit(0)
    except Exception:
        # On any other error, let it through
        sys.exit(0)


if __name__ == '__main__':
    main()