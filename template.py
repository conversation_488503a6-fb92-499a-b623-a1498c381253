from dataclasses import dataclass, field
from typing import Any, Optional, Dict, List


@dataclass
class KeyframeConfig:
    """Represents a single keyframe configuration from TOML"""
    relative: Dict[str, Any]  # type: pre/middle/tail, position: start/end, offset_frame, index?
    scale: Optional[Dict[str, float]] = None  # x, y
    position: Optional[Dict[str, float]] = None  # x, y
    rotation: Optional[float] = None
    alpha: Optional[float] = None
    
    @classmethod
    def from_dict(cls, data: dict) -> 'KeyframeConfig':
        """Create KeyframeConfig from dictionary"""
        return cls(
            relative=data['relative'],
            scale=data.get('scale'),
            position=data.get('position'),
            rotation=data.get('rotation'),
            alpha=data.get('alpha')
        )


@dataclass
class TrackTemplate:
    """Represents a track with its effects and keyframes"""
    type: str  # video/audio/effect/image/text
    timing: str  # relative/absolute
    keyframes: List[KeyframeConfig] = field(default_factory=list)
    index: Optional[int] = None  # for video tracks - which video material to use
    name: Optional[str] = None  # for effects
    params: Optional[Any] = None  # effect parameters
    mask: Optional[Dict[str, Any]] = None  # Changed from str to Dict
    round_corner: Optional[int] = None
    position: Optional[Dict[str, float]] = None
    feather: Optional[int] = None
    center: Optional[Dict[str, float]] = None
    image: Optional[str] = None  # for image tracks
    alpha: Optional[float] = None  # for image tracks
    
    # Attached effect fields (for video segments)
    effect: Optional[str] = None  # effect name to attach to video segment
    
    # Track lifecycle range (optional) - defines when track is active
    range: Optional[Dict[str, Any]] = None  # start/end with type, position
    
    @classmethod
    def from_dict(cls, data: dict) -> 'TrackTemplate':
        """Create TrackTemplate from dictionary"""
        # Extract keyframes if present
        keyframes = []
        if 'keyframe' in data:
            keyframes = [KeyframeConfig.from_dict(kf) for kf in data['keyframe']]
        
        return cls(
            type=data['type'],
            timing=data['timing'],
            keyframes=keyframes,
            index=data.get('index'),
            name=data.get('name'),
            params=data.get('params'),
            mask=data.get('mask'),
            round_corner=data.get('round_corner'),
            position=data.get('position'),
            feather=data.get('feather'),
            center=data.get('center'),
            image=data.get('image'),
            alpha=data.get('alpha'),
            effect=data.get('effect'),
            range=data.get('range')
        )


@dataclass
class ParagraphTemplate:
    """Template for a complete paragraph section"""
    tracks: List[TrackTemplate] = field(default_factory=list)
    
    @classmethod
    def from_dict(cls, data: dict) -> 'ParagraphTemplate':
        """Create ParagraphTemplate from dictionary"""
        tracks = []
        if 'track' in data:
            tracks = [TrackTemplate.from_dict(track) for track in data['track']]
        return cls(tracks=tracks)


@dataclass
class VideoTemplate:
    """Main template container"""
    meta: Dict[str, Any]
    paragraphs: List[ParagraphTemplate] = field(default_factory=list)
    
    @classmethod
    def from_dict(cls, data: dict) -> 'VideoTemplate':
        """Create VideoTemplate from dictionary"""
        meta = data.get('meta', {})
        paragraphs = []
        if 'paragraph' in data:
            paragraphs = [ParagraphTemplate.from_dict(p) for p in data['paragraph']]
        return cls(meta=meta, paragraphs=paragraphs)