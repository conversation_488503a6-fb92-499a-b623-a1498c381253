#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# ///

"""
Log session info as a separate hook.
Can be configured independently from load_source.
"""

import json
import sys
from pathlib import Path
from datetime import datetime


def log_session(session_data):
    """
    Append session data to log file.
    Simple JSON array format.
    """
    log_dir = Path("logs")
    log_dir.mkdir(parents=True, exist_ok=True)
    log_file = log_dir / "session_start.json"
    
    # Add timestamp to session data
    session_data['logged_at'] = datetime.now().isoformat()
    
    # Read existing or start fresh
    log_data = []
    if log_file.exists():
        try:
            with open(log_file, 'r') as f:
                log_data = json.load(f)
        except (json.JSONDecodeError, ValueError):
            pass
    
    # Append and write
    log_data.append(session_data)
    with open(log_file, 'w') as f:
        json.dump(log_data, f, indent=2)


def main():
    """
    Main entry point for SessionStart hook logging.
    Reads JSON from stdin, logs it, exits silently.
    """
    try:
        input_data = json.loads(sys.stdin.read())
        log_session(input_data)
        sys.exit(0)
    except Exception:
        # Silent fail
        sys.exit(0)


if __name__ == "__main__":
    main()