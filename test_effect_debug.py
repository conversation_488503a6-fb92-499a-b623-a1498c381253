import pyJianYingDraft as draft
from pyJianYingDraft import VideoSceneEffectType

# Create script
sf = draft.ScriptFile(1080, 1920)

# Add video track
sf.add_track(draft.TrackType.video, "video_0")

# Create a simple video material (using image as fallback)
import os
if os.path.exists("图片合集/black.png"):
    mat = draft.VideoMaterial("图片合集/black.png")
    print("Using image as video material")
else:
    print("No material file found, creating empty segment")
    # This will fail, but let's see the error
    mat = None

if mat:
    # Create video segment
    seg = draft.VideoSegment(
        mat,  # Pass material directly
        source_timerange=draft.trange(0, 5000000),
        target_timerange=draft.trange(0, 5000000)
    )
    
    # Try to add effect
    print("\nTrying to add effect to segment...")
    effect_type = VideoSceneEffectType.模糊
    print(f"Effect type: {effect_type}")
    print(f"Effect type value: {effect_type.value}")
    
    # Add effect with params
    result = seg.add_effect(effect_type, [0.5])
    print(f"add_effect returned: {result}")
    
    # Check if effect was added
    if hasattr(seg, 'effects'):
        print(f"Segment effects: {seg.effects}")
    else:
        print("Segment has no 'effects' attribute")
    
    # Check segment attributes
    print("\nSegment attributes:")
    for attr in dir(seg):
        if not attr.startswith('_') and 'effect' in attr.lower():
            print(f"  {attr}: {getattr(seg, attr, 'N/A')}")
    
    # Add segment to script
    sf.add_segment(seg, "video_0")
    
    # Also add the material to script
    sf.add_material(mat)
    
    print("\nSegment added to script")
    
    # Save to test
    folder = draft.DraftFolder("C:\\All\\Tools\\JianyingPro Drafts")
    script = folder.create_draft("test_effect_debug", sf.width, sf.height)
    
    # Copy everything from sf to script
    for track_name, track in sf.tracks.items():
        if track_name not in script.tracks:
            script.add_track(track.type, track_name)
    
    for mat_id, mat in sf.materials.items():
        if mat_id not in script.materials:
            script.materials[mat_id] = mat
    
    script.dump()
    print(f"Draft saved to test_effect_debug")