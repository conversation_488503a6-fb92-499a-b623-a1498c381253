import pyJianYingDraft as draft
from typing import Any
from template import <PERSON>Template, ParagraphTemplate, TrackTemplate, KeyframeConfig
from utils import px_to_position
from track import track_exists


class TemplateProcessor:
    """Applies templates to video segments"""
    
    def __init__(self, canvas_width: int = 1080, canvas_height: int = 1920) -> None:
        # Store canvas dimensions for position calculations
        self.canvas_width: int = canvas_width
        self.canvas_height: int = canvas_height
        
        # Map track types to pyJianYingDraft TrackType enum
        # Note: images also use video track type
        self.track_type_map: dict[str, draft.TrackType] = {
            'video': draft.TrackType.video,
            'image': draft.TrackType.video,  # Images are added to video track
            'audio': draft.TrackType.audio,
            'text': draft.TrackType.text,
            'effect': draft.TrackType.effect,
        }
    
    def calculate_range_time(self, range_point: dict[str, Any],
                            paragraph_data: dict[str, Any]) -> int:
        """
        Calculate actual timestamp for a range point (start or end)
        
        Args:
            range_point: Dictionary with type and position
            paragraph_data: Dictionary containing pre/middle/tail timing data
        
        Returns:
            Calculated timestamp in microseconds
        """
        segment_type = range_point['type']  # pre/middle/tail
        position = range_point['position']  # start/end
        offset_frame = range_point.get('offset_frame', 0)
        
        # Frame to microseconds conversion (assuming 30fps)
        frame_duration_us = 33333  # ~33.333ms per frame at 30fps
        offset_us = offset_frame * frame_duration_us
        
        # Get the appropriate segment data (times in microseconds)
        if segment_type == 'pre':
            segment = paragraph_data.get('pre', {})
            start_time = segment.get('start', 0)
            duration = segment.get('duration', 0)
            
        elif segment_type == 'middle':
            # Handle indexed middle segments
            index = range_point.get('index', -1)  # Default to last middle
            middle_segments = paragraph_data.get('middle', [])
            
            if not middle_segments:
                start_time = 0
                duration = 0
            elif index == -1 or index >= len(middle_segments):
                # Use last middle segment
                segment = middle_segments[-1]
                start_time = segment.get('start', 0)
                duration = segment.get('duration', 0)
            else:
                segment = middle_segments[index]
                start_time = segment.get('start', 0)
                duration = segment.get('duration', 0)
                
        elif segment_type == 'tail':
            segment = paragraph_data.get('tail', {})
            start_time = segment.get('start', 0)
            duration = segment.get('duration', 0)
            
        else:
            raise ValueError(f"Invalid segment type: {segment_type}")
        
        # Calculate base time based on position
        if position == 'start':
            base_time = start_time
        elif position == 'end':
            base_time = start_time + duration
        else:
            raise ValueError(f"Invalid position: {position}")
        
        # Apply offset and return
        final_time = base_time + offset_us
        
        # Ensure time is not negative
        return max(0, final_time)
    
    def calculate_keyframe_time(self, keyframe_config: KeyframeConfig, 
                               paragraph_data: dict[str, Any]) -> int:
        """
        Calculate actual timestamp based on relative positioning
        
        Args:
            keyframe_config: The keyframe configuration with relative positioning
            paragraph_data: Dictionary containing pre/middle/tail timing data
        
        Returns:
            Calculated timestamp in microseconds
        """
        relative = keyframe_config.relative
        segment_type = relative['type']  # pre/middle/tail
        position = relative['position']  # start/end
        offset_frame = relative.get('offset_frame', 0)
        
        # Frame to microseconds conversion (assuming 30fps)
        frame_duration_us = 33333  # ~33.333ms per frame at 30fps
        offset_us = offset_frame * frame_duration_us
        
        # Get the appropriate segment data (times in microseconds)
        if segment_type == 'pre':
            segment = paragraph_data.get('pre', {})
            start_time = segment.get('start', 0)
            duration = segment.get('duration', 0)
            
        elif segment_type == 'middle':
            # Handle indexed middle segments
            index = relative.get('index', 0)
            middle_segments = paragraph_data.get('middle', [])
            
            if index >= len(middle_segments):
                # Fallback to last middle segment if index out of range
                index = len(middle_segments) - 1 if middle_segments else 0
            
            if middle_segments and index >= 0:
                segment = middle_segments[index]
                start_time = segment.get('start', 0)
                duration = segment.get('duration', 0)
            else:
                start_time = 0
                duration = 0
                
        elif segment_type == 'tail':
            segment = paragraph_data.get('tail', {})
            start_time = segment.get('start', 0)
            duration = segment.get('duration', 0)
            
        else:
            raise ValueError(f"Invalid segment type: {segment_type}")
        
        # Calculate base time based on position
        if position == 'start':
            base_time = start_time
        elif position == 'end':
            base_time = start_time + duration
        else:
            raise ValueError(f"Invalid position: {position}")
        
        # Apply offset and return
        final_time = base_time + offset_us
        
        # Ensure time is not negative
        return max(0, final_time)
    
    def apply_keyframe(self, segment: draft.VideoSegment, keyframe_config: KeyframeConfig, 
                      timestamp: int) -> None:
        """
        Apply keyframe properties to segment at timestamp
        
        Args:
            segment: The pyJianYingDraft segment object
            keyframe_config: The keyframe configuration
            timestamp: The calculated timestamp in microseconds
        """
        # Use the segment's add_keyframe method for each property
        if keyframe_config.scale:
            if 'x' in keyframe_config.scale and 'y' in keyframe_config.scale:
                # If both x and y are the same, use uniform scale
                if keyframe_config.scale['x'] == keyframe_config.scale['y']:
                    segment.add_keyframe(
                        draft.KeyframeProperty.uniform_scale,
                        timestamp,
                        keyframe_config.scale['x']
                    )
                else:
                    # Different x and y scales
                    segment.add_keyframe(
                        draft.KeyframeProperty.scale_x,
                        timestamp,
                        keyframe_config.scale['x']
                    )
                    segment.add_keyframe(
                        draft.KeyframeProperty.scale_y,
                        timestamp,
                        keyframe_config.scale['y']
                    )
            elif 'x' in keyframe_config.scale:
                segment.add_keyframe(
                    draft.KeyframeProperty.scale_x,
                    timestamp,
                    keyframe_config.scale['x']
                )
            elif 'y' in keyframe_config.scale:
                segment.add_keyframe(
                    draft.KeyframeProperty.scale_y,
                    timestamp,
                    keyframe_config.scale['y']
                )
        
        if keyframe_config.position:
            if 'x' in keyframe_config.position:
                # Use px_to_position from utils.py for normalization
                segment.add_keyframe(
                    draft.KeyframeProperty.position_x,
                    timestamp,
                    px_to_position(int(keyframe_config.position['x']), self.canvas_width)
                )
            
            if 'y' in keyframe_config.position:
                # Use px_to_position from utils.py for normalization
                segment.add_keyframe(
                    draft.KeyframeProperty.position_y,
                    timestamp,
                    px_to_position(int(keyframe_config.position['y']), self.canvas_height)
                )
        
        if keyframe_config.rotation is not None:
            segment.add_keyframe(
                draft.KeyframeProperty.rotation,
                timestamp,
                keyframe_config.rotation
            )
        
        if keyframe_config.alpha is not None:
            segment.add_keyframe(
                draft.KeyframeProperty.alpha,
                timestamp,
                keyframe_config.alpha
            )
    
    def create_segment_from_track(self, track: TrackTemplate, 
                                 paragraph_data: dict[str, Any],
                                 materials: dict[str, Any]) -> Any | None:
        """
        Create appropriate segment based on track type
        
        Args:
            track: The track template
            paragraph_data: Paragraph timing data
            materials: Available materials (videos, images, etc.)
        
        Returns:
            Created segment or None
        """
        # Calculate paragraph timing information
        pre_start: int = paragraph_data.get('pre', {}).get('start', 0)
        pre_duration: int = paragraph_data.get('pre', {}).get('duration', 0)
        middle_durations: int = sum(s.get('duration', 0) 
                             for s in paragraph_data.get('middle', []))
        tail_duration: int = paragraph_data.get('tail', {}).get('duration', 0)
        
        # Check if track has a custom range
        if track.range:
            # Calculate custom start and end times based on range
            track_start = self.calculate_range_time(track.range['start'], paragraph_data)
            track_end = self.calculate_range_time(track.range['end'], paragraph_data)
            
            # Validate range
            if track_end <= track_start:
                print(f"Warning: Invalid range (end <= start) for track {track.type}, skipping")
                return None
            
            paragraph_start = track_start
            total_duration = track_end - track_start
        else:
            # Default behavior: use entire paragraph
            paragraph_start: int = pre_start
            total_duration: int = pre_duration + middle_durations + tail_duration
        
        if track.type == 'video':
            # Select video material based on track.index
            video_index = track.index if track.index is not None else 0  # Default to first video
            if 'videos' not in materials or not materials['videos']:
                print(f"Warning: No video materials available for track with index {video_index}")
                return None
            
            # Handle index out of range
            if video_index >= len(materials['videos']):
                print(f"Warning: Video index {video_index} out of range, using first video")
                video_index = 0
            
            video_path: str = materials['videos'][video_index]
            
            # Check if video file exists
            import os
            if not os.path.exists(video_path):
                print(f"Warning: Video file not found: {video_path}, skipping this segment")
                return None
                
            video_material = draft.VideoMaterial(video_path)
            
            # Create segment based on timing mode
            if track.timing == 'relative':
                # Relative timing: source time = target time
                # The segment uses the same time range from the material as its position on timeline
                # Check if we have enough material starting from paragraph_start
                available_duration = min(total_duration, video_material.duration - paragraph_start)
                
                if available_duration <= 0:
                    print(f"Warning: No available duration for relative timing at position {paragraph_start}")
                    return None
                
                target_timerange = draft.trange(paragraph_start, paragraph_start + available_duration)
                source_timerange = draft.trange(paragraph_start, paragraph_start + available_duration)
                
                segment = draft.VideoSegment(
                    video_material,
                    target_timerange,
                    source_timerange=source_timerange
                )
            else:  # absolute timing
                # Absolute timing: source always starts from 0
                # The segment uses material from beginning regardless of timeline position
                available_duration = min(total_duration, video_material.duration)
                
                target_timerange = draft.trange(paragraph_start, paragraph_start + available_duration)
                source_timerange = draft.trange(0, available_duration)
                
                segment = draft.VideoSegment(
                    video_material,
                    target_timerange,
                    source_timerange=source_timerange
                )
            
            # Apply mask if specified
            if track.mask:
                # Get mask type from configuration
                if 'type' not in track.mask:
                    raise ValueError("Mask configuration missing required 'type' field")
                
                mask_type_str = track.mask['type']
                
                # Try to get the mask type from pyJianYingDraft
                try:
                    mask_type = getattr(draft.MaskType, mask_type_str)
                except AttributeError:
                    raise ValueError(f"Unknown mask type '{mask_type_str}'")
                
                # Prepare mask parameters with proper normalization
                mask_params = {
                    'mask_type': mask_type,
                }
                
                # Normalize position if provided (px to [-1, 1] range)
                if 'position' in track.mask:
                    if 'x' in track.mask['position']:
                        mask_params['center_x'] = px_to_position(
                            track.mask['position']['x'], 
                            self.canvas_width
                        )
                    if 'y' in track.mask['position']:
                        mask_params['center_y'] = px_to_position(
                            track.mask['position']['y'],
                            self.canvas_height
                        )
                
                # Normalize center if provided (different from position)
                if 'center' in track.mask:
                    if 'x' in track.mask['center']:
                        mask_params['center_x'] = px_to_position(
                            track.mask['center']['x'],
                            self.canvas_width
                        )
                    if 'y' in track.mask['center']:
                        mask_params['center_y'] = px_to_position(
                            track.mask['center']['y'],
                            self.canvas_height
                        )
                
                # Add feather if specified
                if 'feather' in track.mask:
                    mask_params['feather'] = track.mask['feather']  # Normalize to 0-100
                
                # Add round corner for rectangle masks
                if 'round_corner' in track.mask and mask_type == draft.MaskType.矩形:
                    mask_params['round_corner'] = track.mask['round_corner']  # Normalize to 0-100
                
                # Add size parameter if specified
                if 'size' in track.mask:
                    mask_params['size'] = track.mask['size']
                
                # Add rotation parameter if specified (assuming degrees)
                if 'rotation' in track.mask:
                    mask_params['rotation'] = track.mask['rotation']
                
                # Add rect_width for rectangular masks if specified
                if 'rect_width' in track.mask and mask_type == draft.MaskType.矩形:
                    mask_params['rect_width'] = track.mask['rect_width']
                
                # Add invert parameter if specified
                if 'invert' in track.mask:
                    mask_params['invert'] = track.mask['invert']
                
                # Apply the mask with all parameters
                segment.add_mask(**mask_params)
            
            # Check if the video segment has an attached effect
            if hasattr(track, 'effect') and track.effect:
                # Apply effect directly to the video segment
                effect_name = track.effect
                print(f"Processing attached effect: {effect_name}")
                
                # Get the effect type
                try:
                    effect_type = getattr(draft.VideoSceneEffectType, effect_name)
                    print(f"Found effect type: {effect_type}")
                except AttributeError:
                    print(f"Warning: Unknown effect type '{effect_name}'")
                    effect_type = None
                
                if effect_type:
                    # Process params - can be a single value or list
                    params = None
                    if hasattr(track, 'params'):
                        if isinstance(track.params, (int, float)):
                            params = [float(track.params)]
                        elif isinstance(track.params, list):
                            params = [float(p) for p in track.params]
                    
                    # Add effect to the video segment
                    # Cast to proper type for pyJianYingDraft
                    segment.add_effect(effect_type, params)  # type: ignore
                    print(f"Added effect '{effect_name}' to video segment")
            
            return segment
                
        elif track.type == 'effect':
            # Create effect segment
            if not track.name:
                print("Warning: Effect track missing 'name' field")
                return None
            
            # Calculate effect timerange based on timing mode
            if track.timing == 'relative':
                # Effect covers the whole paragraph
                target_timerange = draft.trange(paragraph_start, paragraph_start + total_duration)
            else:  # absolute timing
                # Effect from start to paragraph end
                target_timerange = draft.trange(0, paragraph_start + total_duration)
            
            # The enum attributes use Chinese names directly
            # No need for mapping - use the Chinese name as-is
            effect_enum_name = track.name
            
            # Use getattr to get the effect type from enum
            try:
                effect_type = getattr(draft.VideoSceneEffectType, effect_enum_name)
            except AttributeError:
                print(f"Warning: Unknown effect type '{track.name}' (enum name: '{effect_enum_name}')")
                # Use a simple effect as fallback
                effect_type = draft.VideoSceneEffectType._1998
            
            # Process params - can be a single value or list
            params = None
            if hasattr(track, 'params'):
                if isinstance(track.params, (int, float)):
                    params = [float(track.params)]
                elif isinstance(track.params, list):
                    params = [float(p) for p in track.params]
            
            # Return effect info in a dict (not an actual segment)
            # This will be handled specially in process_paragraph
            return {
                'type': 'effect',
                'effect_type': effect_type,
                'target_timerange': target_timerange,
                'params': params
            }
                
        elif track.type == 'image':
            # Create image segment using VideoSegment with ImageMaterial
            if not track.image:
                print("Warning: Image track missing 'image' field")
                return None
            
            # Check if image file exists
            import os
            if not os.path.exists(track.image):
                print(f"Warning: Image file not found: {track.image}, skipping this segment")
                return None
            
            # Create VideoMaterial from the image path (images use VideoMaterial in pyJianYingDraft)
            image_material = draft.VideoMaterial(track.image)
            
            # Use the paragraph_start and total_duration which already account for range if specified
            target_timerange = draft.trange(paragraph_start, paragraph_start + total_duration)
            
            # Create VideoSegment with ImageMaterial
            segment = draft.VideoSegment(
                image_material,
                target_timerange
            )
            
            # Apply alpha if specified in track config
            if hasattr(track, 'alpha') and track.alpha is not None:
                segment.add_keyframe(
                    draft.KeyframeProperty.alpha,
                    paragraph_start,  # Set alpha at start
                    track.alpha
                )
            
            # Apply mask if specified (same logic as video)
            if track.mask:
                # Get mask type from configuration
                if 'type' not in track.mask:
                    raise ValueError("Mask configuration missing required 'type' field")
                
                mask_type_str = track.mask['type']
                
                # Try to get the mask type from pyJianYingDraft
                try:
                    mask_type = getattr(draft.MaskType, mask_type_str)
                except AttributeError:
                    raise ValueError(f"Unknown mask type '{mask_type_str}'")
                
                # Prepare mask parameters with proper normalization
                mask_params = {
                    'mask_type': mask_type,
                }
                
                # Normalize position if provided (px to [-1, 1] range)
                if 'position' in track.mask:
                    if 'x' in track.mask['position']:
                        mask_params['center_x'] = px_to_position(
                            track.mask['position']['x'], 
                            self.canvas_width
                        )
                    if 'y' in track.mask['position']:
                        mask_params['center_y'] = px_to_position(
                            track.mask['position']['y'],
                            self.canvas_height
                        )
                
                # Normalize center if provided (different from position)
                if 'center' in track.mask:
                    if 'x' in track.mask['center']:
                        mask_params['center_x'] = px_to_position(
                            track.mask['center']['x'],
                            self.canvas_width
                        )
                    if 'y' in track.mask['center']:
                        mask_params['center_y'] = px_to_position(
                            track.mask['center']['y'],
                            self.canvas_height
                        )
                
                # Add feather if specified
                if 'feather' in track.mask:
                    mask_params['feather'] = track.mask['feather'] / 100.0
                
                # Add round corner for rectangle masks
                if 'round_corner' in track.mask and mask_type == draft.MaskType.矩形:
                    mask_params['round_corner'] = track.mask['round_corner'] / 100.0
                
                # Add other optional parameters
                if 'size' in track.mask:
                    mask_params['size'] = track.mask['size']
                if 'rotation' in track.mask:
                    mask_params['rotation'] = track.mask['rotation']
                if 'rect_width' in track.mask and mask_type == draft.MaskType.矩形:
                    mask_params['rect_width'] = track.mask['rect_width']
                if 'invert' in track.mask:
                    mask_params['invert'] = track.mask['invert']
                
                # Apply the mask with all parameters
                segment.add_mask(**mask_params)
            
            return segment
                
        elif track.type == 'audio':
            # Create audio segment
            # TODO: Implement audio segment creation
            pass
            
        elif track.type == 'text':
            # Create text segment
            # TODO: Implement text segment creation
            pass
        
        return None
    
    def process_paragraph(self, script: Any, paragraph_template: ParagraphTemplate,
                         paragraph_data: dict[str, Any], materials: dict[str, Any]) -> None:
        """
        Process one paragraph with all its tracks
        
        Args:
            script: The pyJianYingDraft script object
            paragraph_template: The paragraph template
            paragraph_data: Paragraph timing data (pre/middle/tail)
            materials: Available materials (videos, images, etc.)
        """
        for track_index, track in enumerate(paragraph_template.tracks):
            # Create segment based on track type and timing mode
            # Both absolute and relative timing are handled within create_segment_from_track
            segment = self.create_segment_from_track(track, paragraph_data, materials)
            
            if segment:
                # Apply all keyframes to the segment
                # Note: For absolute timing tracks, keyframe timestamps might need special handling
                # Currently using the same calculation for both timing modes
                if hasattr(track, 'keyframes') and track.keyframes:
                    for keyframe_config in track.keyframes:
                        timestamp = self.calculate_keyframe_time(
                            keyframe_config, paragraph_data
                        )
                        self.apply_keyframe(segment, keyframe_config, timestamp)
                
                # Handle different types of segments
                if track.type == 'effect' and isinstance(segment, dict) and segment.get('type') == 'effect':
                    # Effects need special handling - use add_effect instead of add_segment
                    track_name = f'effect_{track_index}'
                    
                    # Ensure effect track exists
                    if not track_exists(script, track_name):
                        script.add_track(draft.TrackType.effect, track_name, relative_index=track_index)
                    
                    # Add the effect to the track
                    script.add_effect(
                        effect=segment['effect_type'],
                        t_range=segment['target_timerange'],
                        track_name=track_name,
                        params=segment['params']
                    )
                else:
                    # Regular segments (video, audio, text)
                    track_type = self.track_type_map.get(track.type)
                    if track_type:
                        # Generate unique track names based on array index
                        if track.type in ['video', 'image']:
                            # Both video and image use video track type
                            track_name = f'video_{track_index}'
                        else:
                            # For audio, text, etc.
                            track_name = f'{track.type}_{track_index}'
                        
                        if not track_exists(script, track_name):
                            # Add track with relative_index based on track_index
                            # Higher index means higher layer (closer to top)
                            script.add_track(track_type, track_name, relative_index=track_index)
                        
                        # Add segment to specific track by name
                        script.add_segment(segment, track_name)
    
    def process_template(self, script: Any, template: VideoTemplate,
                        paragraphs_data: list[dict[str, Any]], 
                        materials: dict[str, Any]) -> None:
        """
        Apply complete template to script
        
        Args:
            script: The pyJianYingDraft script object
            template: The video template
            paragraphs_data: List of paragraph timing data
            materials: Available materials
        """
        # Process each paragraph in the template
        for i, paragraph_template in enumerate(template.paragraphs):
            if i < len(paragraphs_data):
                paragraph_data = paragraphs_data[i]
                self.process_paragraph(
                    script, paragraph_template, paragraph_data, materials
                )