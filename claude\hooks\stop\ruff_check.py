#!/usr/bin/env python3
"""
Linus-style ruff checker for modified files.
No fancy abstractions. Check what changed, report problems, done.
"""

import json
import sys
import subprocess
from typing import cast

def main():
    # Read input - <PERSON> sends JSO<PERSON> via stdin
    try:
        data = cast(dict[str, str | bool], json.load(sys.stdin))
    except json.JSONDecodeError as e:
        print(f"Bad JSON: {e}", file=sys.stderr)
        sys.exit(1)
    
    # Prevent infinite loops - if we're already in a stop hook, bail
    if data.get('stop_hook_active'):
        sys.exit(0)
    
    # Get modified Python files - simple git diff
    result = subprocess.run(
        ['git', 'diff', '--name-only', 'HEAD', '--', '*.py'],
        capture_output=True,
        text=True
    )
    
    if result.returncode != 0:
        # Git failed? Not our problem, let <PERSON> stop
        sys.exit(0)
    
    modified_files = [f for f in result.stdout.strip().split('\n') if f]
    
    if not modified_files:
        # No Python files changed, we're done
        sys.exit(0)
    
    # Run ruff on modified files
    ruff_result = subprocess.run(
        ['ruff', 'check'] + modified_files,
        capture_output=True,
        text=True
    )
    
    # Clean? Let it through
    if ruff_result.returncode == 0:
        sys.exit(0)
    
    # Problems found - block stop and report
    # Exit code 2 blocks stop and feeds stderr to Claude
    print(f"""Ruff found issues. Fix these before stopping:

{ruff_result.stdout}
Files checked: {', '.join(modified_files)}""", file=sys.stderr)
    
    sys.exit(2)

if __name__ == '__main__':
    main()