[meta]
name = "初始模板"
videos = 1
images = 1
canvas_width = 1080
canvas_height = 1920

[[paragraph]]

    [[paragraph.track]]
    type = "video"
    index = 0
    timing = "relative"
    effect = "模糊"
    params = 50


    [[paragraph.track]]
    type = "image"
    timing = "absolute"
    image = "图片合集/black.png"
    alpha = 0.0
    range = { start = {type = "pre", position = "start"}, end = {type = "middle", position = "end" }}

        [[paragraph.track.keyframe]]
        relative = { type = "pre", position = "start", offset_frame = 0}
        alpha = 0.0

        [[paragraph.track.keyframe]]
        relative = { type = "pre", position = "start", offset_frame = 20}
        alpha = 0.8

        [[paragraph.track.keyframe]]
        relative = { type = "middle", position = "end", offset_frame = -20}
        alpha = 0.8

        [[paragraph.track.keyframe]]
        relative = { type = "middle", position = "end", offset_frame = 0}
        alpha = 0.0

    [[paragraph.track]]
    type = "video"
    index = 1
    timing = "relative"
    mask = { type = "矩形", round_corner = 20, position = { x = 26, y = 173 }, feather = 1, center = { x = 700, y = 900 } }
    range = { start = {type = "pre", position = "start"}, end = {type = "middle", position = "end" }}

        [[paragraph.track.keyframe]]
        relative = { type = "pre", position = "start", offset_frame = 0}
        position = { x = 0, y = 0}
        scale = { x = 1.0, y = 1.0}

        [[paragraph.track.keyframe]]
        relative = { type = "pre", position = "start", offset_frame = 20}
        scale = { x = 0.7, y = 0.7}
        position = { x = 0, y = -900}

        [[paragraph.track.keyframe]]
        relative = { type = "middle", position = "start", offset_frame = 0}
        scale = { x = 0.7, y = 0.7}
        position = { x = 0, y = -900}

        [[paragraph.track.keyframe]]
        relative = { type = "middle", position = "start", offset_frame = 10}
        scale = { x = 0.8, y = 0.8}
        position = { x = 0, y = 400}

        [[paragraph.track.keyframe]]
        relative = { type = "middle", position = "end", offset_frame = -10}
        scale = { x = 0.8, y = 0.8}
        position = { x = 0, y = 400}


        [[paragraph.track.keyframe]]
        relative = { type = "middle", position = "end", offset_frame = 0}
        scale = { x = 1.0, y = 1.0}
        position = { x = 0, y = 0}




