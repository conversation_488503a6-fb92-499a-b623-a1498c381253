#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# ///

import json
import sys
from pathlib import Path
from datetime import datetime, timezone


# --- Git helper -----------------------------------------------------------

def init_log_dir():
    """
    Create logs directory if it doesn't exist.
    Returns the log directory path.
    """
    log_dir = Path("logs")
    log_dir.mkdir(parents=True, exist_ok=True)
    return log_dir


def read_log_file(log_file):
    """
    Read existing log data from file.
    Returns list of log entries, empty list on error.
    """
    if not log_file.exists():
        return []
    
    try:
        with open(log_file, 'r') as f:
            data = json.load(f)
            if isinstance(data, list):
                return data
            return []
    except (json.JSONDecodeError, ValueError, OSError):
        return []


def write_log_file(log_file, data):
    """
    Write log data to file with proper formatting.
    Returns True on success, False on error.
    """
    try:
        with open(log_file, 'w') as f:
            json.dump(data, f, indent=2)
        return True
    except OSError:
        return False


def main():
    try:
        # Read JSON input from stdin
        input_data = json.loads(sys.stdin.read())
        
        # Extract session_id
        session_id = input_data.get('session_id', 'unknown')
        
        # Initialize log directory
        log_dir = init_log_dir()
        log_file = log_dir / 'user_prompt_submit.json'
        
        # Read existing logs
        log_data = read_log_file(log_file)
        
        # Create new log entry
        log_entry = {
            "logged_at": datetime.now(timezone.utc).isoformat(),
            "session_id": session_id,
            **input_data,
        }
        
        log_data.append(log_entry)
        
        # Write back to file
        write_log_file(log_file, log_data)

        # Add timestamp to context (this will be added to the prompt)
        print(f"Current time: {datetime.now()}")
        
        # Success - let the prompt through
        sys.exit(0)
        
    except json.JSONDecodeError:
        # If we can't parse JSON, let it through
        sys.exit(0)
    except Exception:
        # On any other error, let it through
        sys.exit(0)


if __name__ == '__main__':
    main()