[project]
name = "jianying-automation"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
[tool.basedpyright]
typeCheckingMode = "recommended"
dependencies = [
    "aiofiles==24.1.0",
    "annotated-types==0.7.0",
    "anyio==4.9.0",
    "basedpyright>=1.31.2",
    "brotli==1.1.0",
    "cachetools==5.5.2",
    "certifi==2025.7.14",
    "charset-normalizer==3.4.2",
    "click==8.2.2",
    "colorama==0.4.6",
    "comtypes==1.4.11",
    "distro==1.9.0",
    "dotenv==0.9.9",
    "fastapi==0.116.1",
    "ffmpy==0.6.1",
    "filelock==3.18.0",
    "fsspec==2025.7.0",
    "google-api-core==2.25.1",
    "google-auth==2.40.3",
    "google-cloud-language==2.17.2",
    "google-genai==1.28.0",
    "googleapis-common-protos==1.70.0",
    "gradio==5.39.0",
    "gradio-client==1.11.0",
    "groovy==0.1.2",
    "grpcio==1.74.0",
    "grpcio-status==1.74.0",
    "h11==0.16.0",
    "httpcore==1.0.9",
    "httpx==0.28.1",
    "huggingface-hub==0.34.3",
    "idna==3.10",
    "imageio==2.37.0",
    "jinja2==3.1.6",
    "jiter==0.10.0",
    "markdown-it-py==3.0.0",
    "markupsafe==3.0.2",
    "mdurl==0.1.2",
    "numpy==2.2.6",
    "openai==1.98.0",
    "opencv-python==*********",
    "orjson==3.11.1",
    "packaging==25.0",
    "pandas==2.3.1",
    "pillow==11.3.0",
    "proto-plus==1.26.1",
    "protobuf==6.31.1",
    "pyasn1==0.6.1",
    "pyasn1-modules==0.4.2",
    "pydantic==2.11.7",
    "pydantic-core==2.33.2",
    "pydub==0.25.1",
    "pygments==2.19.2",
    "pyjianyingdraft==0.2.2",
    "pymediainfo==7.0.1",
    "python-dateutil==2.9.0.post0",
    "python-dotenv==1.1.1",
    "python-multipart==0.0.20",
    "pytz==2025.2",
    "pywin32==311",
    "pyyaml==6.0.2",
    "requests==2.32.4",
    "rich==14.1.0",
    "rsa==4.9.1",
    "ruff==0.12.7",
    "safehttpx==0.1.6",
    "semantic-version==2.10.0",
    "shellingham==1.5.4",
    "six==1.17.0",
    "sniffio==1.3.1",
    "starlette==0.47.2",
    "tenacity==8.5.0",
    "toml>=0.10.2",
    "tomlkit==0.13.3",
    "tqdm==4.67.1",
    "typer==0.16.0",
    "typing-extensions==4.14.1",
    "typing-inspection==0.4.1",
    "tzdata==2025.2",
    "uiautomation==2.0.28",
    "urllib3==2.5.0",
    "uvicorn==0.35.0",
    "websockets==15.0.1",
]
