# 视频模板系统文档

## 概述
本系统实现了基于 TOML 配置的视频模板系统，支持关键帧动画、相对时间定位和多轨道处理。

## 时间单位说明

### 统一使用微秒
- **单位**: 微秒 (microseconds, μs)
- **常量**: `draft.SEC = 1000000` (1秒 = 1,000,000 微秒)
- **与 pyJianYingDraft API 保持一致**

### paragraph.json 时间格式
```json
{
    "pre": {
        "start": 0,
        "duration": 1000000  // 1秒
    },
    "middle": [
        {
            "start": 1000000,     // 1秒
            "duration": 1000000   // 1秒
        }
    ]
}
```

## 画布尺寸配置

在 `config.toml` 的 `[meta]` 部分定义：

```toml
[meta]
name = "初始模板"
canvas_width = 1080
canvas_height = 1920
```

## 关键帧系统

### 相对定位
```toml
[[paragraph.track.keyframe]]
relative = { type = "pre", position = "start", offset_frame = 0}
```

- `type`: pre/middle/tail - 段落位置
- `position`: start/end - 相对于段落的位置
- `offset_frame`: 帧偏移 (30fps下，1帧 ≈ 33.33ms)
- `index`: (仅middle) 指定段中的句子索引

### 支持的属性
- `scale`: {x, y} - 缩放
- `position`: {x, y} - 位置（像素值，自动归一化）
- `rotation`: 旋转角度
- `alpha`: 透明度 (0-1)

### 位置归一化
位置值自动转换为"半画布单位"：
- x_normalized = x_pixels / (canvas_width / 2)
- y_normalized = y_pixels / (canvas_height / 2)

## 使用示例

### 基本用法
```python
from template_manager import TemplateManager

# 创建管理器
manager = TemplateManager("/path/to/drafts")

# 应用模板
manager.apply_template(
    draft_name="我的项目",
    template_path="config.toml",
    paragraph_json_path="paragraph.json",  # 时间单位：微秒
    video_paths=["video1.mp4", "video2.mp4"],
    image_paths=["cover.png"]
)
```

## 文件结构

```
project/
├── config.toml           # 模板配置（关键帧、效果等）
├── paragraph.json        # 时间数据（微秒）
├── template.py           # 数据类定义
├── template_loader.py    # TOML 加载器
├── template_processor.py # 核心处理器
└── template_manager.py   # 管理器接口
```

## 时间计算示例

假设 paragraph.json (微秒):
```json
{
    "pre": {"start": 0, "duration": 1000000},
    "middle": [
        {"start": 1000000, "duration": 1000000},
        {"start": 2000000, "duration": 1000000}
    ],
    "tail": {"start": 3000000, "duration": 1000000}
}
```

关键帧时间计算结果：
- `pre + start + 0帧` → 0 μs
- `pre + end + -10帧` → 666,670 μs
- `middle[0] + start + 20帧` → 1,666,660 μs
- `tail + end + 0帧` → 4,000,000 μs

## 注意事项

1. **时间单位**: 所有时间值使用**微秒**（1秒 = 1,000,000 微秒）
2. **帧率假设**: 系统假设 30fps，1帧 = 33,333 微秒
3. **位置单位**: 模板中的位置值是像素，会自动归一化
4. **缩放优化**: 当 scale.x == scale.y 时，自动使用 uniform_scale