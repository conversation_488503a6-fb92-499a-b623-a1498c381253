#!/usr/bin/env -S uv run --script
# /// script
# requires-python = ">=3.11"
# ///

"""
Load source context for Claude Code session.
Only loads git status and specified documents.
"""

import json
import sys
import subprocess
from pathlib import Path


def get_git_status():
    """
    Get current git branch and uncommitted changes.
    Simple and direct. No fancy error handling.
    """
    try:
        # Get branch
        result = subprocess.run(
            ['git', 'rev-parse', '--abbrev-ref', 'HEAD'],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=5
        )
        branch = result.stdout.strip() if result.returncode == 0 else None
        
        if not branch:
            return None
        
        # Get status
        result = subprocess.run(
            ['git', 'status', '--porcelain'],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=5
        )
        
        status = ""
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n') if result.stdout.strip() else []
            if lines:
                status = f"\nStatus:\n{result.stdout.strip()}"
        
        # Get recent commits
        result = subprocess.run(
            ['git', 'log', '--oneline', '-5'],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=5
        )
        
        commits = ""
        if result.returncode == 0 and result.stdout.strip():
            commits = f"\n\nRecent commits:\n{result.stdout.strip()}"
        
        # Get main branch name
        result = subprocess.run(
            ['git', 'symbolic-ref', 'refs/remotes/origin/HEAD'],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=5
        )
        main_branch = None
        if result.returncode == 0:
            # Extract branch name from refs/remotes/origin/main
            main_branch = result.stdout.strip().split('/')[-1]
        
        return f"Current branch: {branch}\n\nMain branch (you will usually use this for PRs): {main_branch or ''}{status}{commits}"
        
    except Exception:
        return None


def load_documents():
    """
    Load specified context documents.
    Only loads what exists. No fancy searching.
    """
    docs = [
        "claude/Claude.md",
        "claude/Role - Linus.md",
        "claude/pyJianYingDraft.md"
    ]
    
    contents = []
    for doc_path in docs:
        path = Path(doc_path)
        if path.exists():
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        # Add document name as header
                        contents.append(f"# {doc_path}\n{content}")
            except Exception:
                pass
    
    return "\n\n".join(contents) if contents else None


def main():
    """
    Main entry point for SessionStart hook.
    Reads JSON from stdin, outputs context to stdout.
    """
    try:
        # Read input
        input_data = json.loads(sys.stdin.read())
        _ = input_data.get('source', 'unknown')  # Keep for future use
        
        # Build context
        parts = []
        
        # Add git status
        git_info = get_git_status()
        if git_info:
            parts.append(f"gitStatus: This is the git status at the start of the conversation. Note that this status is a snapshot in time, and will not update during the conversation.\n{git_info}")
        
        # Add documents
        docs = load_documents()
        if docs:
            parts.append("# important-instruction-reminders\nDo what has been asked; nothing more, nothing less.\nNEVER create files unless they're absolutely necessary for achieving your goal.\nALWAYS prefer editing an existing file to creating a new one.\nNEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.\n\n      \n      IMPORTANT: this context may or may not be relevant to your tasks. You should not respond to this context unless it is highly relevant to your task.")
        
        if not parts:
            # Nothing to add
            sys.exit(0)
        
        # Output context using hookSpecificOutput format
        output = {
            "hookSpecificOutput": {
                "hookEventName": "SessionStart",
                "additionalContext": "\n\n".join(parts)
            }
        }
        
        print(json.dumps(output))
        sys.exit(0)
        
    except Exception:
        # Silent fail - hooks should not break Claude
        sys.exit(0)


if __name__ == "__main__":
    main()