#!/usr/bin/env python
"""
Main entry point for the JianYing template automation system
"""

import sys
from pathlib import Path
from template_manager import TemplateManager
import config


def main():
    """Main function to run the template system"""
    
    # Check if the JianYing draft folder exists
    draft_folder = Path(config.BASE_DRAFT_FOLDER)
    if not draft_folder.exists():
        print(f"Error: JianYing draft folder not found at: {config.BASE_DRAFT_FOLDER}")
        print("Please ensure Jian<PERSON><PERSON> is installed and the path in config.py is correct.")
        return 1
    
    # Initialize the template manager
    print(f"Initializing with draft folder: {config.BASE_DRAFT_FOLDER}")
    manager = TemplateManager()
    
    # Example: Apply the default template
    try:
        draft_path = manager.apply_template(
            draft_name="AutoGenerated_Project",
            template_path="test_config.toml",  # Use test config without problematic paths
            paragraph_json_path=config.DEFAULT_PARAGRAPH_JSON_PATH,
            video_paths=[],  # Add your video paths here
            image_paths=[]   # Add your image paths here
        )
        
        print(f"\nSuccess! Draft created at: {draft_path}")
        print("You can now open this draft in JianYing.")
        return 0
        
    except FileNotFoundError as e:
        print(f"\nError: Required file not found: {e}")
        print("Please ensure config.toml and paragraph.json exist.")
        return 1
    except Exception as e:
        print(f"\nError: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())