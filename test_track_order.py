import pyJianYingDraft as draft

# Create a test to verify track ordering
sf = draft.ScriptFile(1080, 1920)

# Add tracks in the order from config.toml
# Index 0: video
sf.add_track(draft.TrackType.video, "video_0", relative_index=0)

# Index 1: effect (should be on layer above video)
sf.add_track(draft.TrackType.effect, "effect_1", relative_index=1)

# Index 2: image (which is actually video type)
sf.add_track(draft.TrackType.video, "video_2", relative_index=2)

# Index 3: another video
sf.add_track(draft.TrackType.video, "video_3", relative_index=3)

print("Tracks created:")
for name, track in sf.tracks.items():
    print(f"  {name}: render_index={track.render_index}")

print("\n轨道层级说明：")
print("- render_index 越大，轨道越在上层")
print("- video 轨道的 render_index=0")
print("- effect 轨道的 render_index=10000（总是在视频上方）")
print("- 同类型轨道内，relative_index 越大越在上层")