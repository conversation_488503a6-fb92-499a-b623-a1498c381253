import toml
from template import VideoTemplate


class TemplateLoader:
    """Load and parse TOML templates"""
    
    def load_template(self, toml_path: str) -> VideoTemplate:
        """Parse TOML file into VideoTemplate object"""
        # Load TOML file
        with open(toml_path, 'r', encoding='utf-8') as f:
            data = toml.load(f)
        
        # Parse and restructure the data for VideoTemplate
        template_data = self._restructure_toml_data(data)
        
        # Create VideoTemplate from parsed data
        return VideoTemplate.from_dict(template_data)
    
    def _restructure_toml_data(self, data: dict[str, any]) -> dict[str, any]:
        """Restructure TOML data to match VideoTemplate structure"""
        result = {
            'meta': data.get('meta', {}),
            'paragraph': []
        }
        
        # Process paragraph sections
        if 'paragraph' in data:
            paragraphs = data['paragraph']
            # Handle both single paragraph and list of paragraphs
            if not isinstance(paragraphs, list):
                paragraphs = [paragraphs]
            
            for paragraph in paragraphs:
                paragraph_data = {'track': []}
                
                # Process tracks in the paragraph
                if 'track' in paragraph:
                    tracks = paragraph['track']
                    # Handle both single track and list of tracks
                    if not isinstance(tracks, list):
                        tracks = [tracks]
                    
                    for track in tracks:
                        track_data = {
                            'type': track.get('type', 'video'),
                            'timing': track.get('timing', 'relative'),
                            'keyframe': []
                        }
                        
                        # Copy other track properties
                        for key in ['index', 'name', 'params', 'mask', 'round_corner', 
                                   'position', 'feather', 'center', 'image', 'alpha', 'effect', 'range']:
                            if key in track:
                                track_data[key] = track[key]
                        
                        # Process keyframes
                        if 'keyframe' in track:
                            keyframes = track['keyframe']
                            # Handle both single keyframe and list of keyframes
                            if not isinstance(keyframes, list):
                                keyframes = [keyframes]
                            track_data['keyframe'] = keyframes
                        
                        paragraph_data['track'].append(track_data)
                
                result['paragraph'].append(paragraph_data)
        
        return result
    
    def validate_template(self, template: VideoTemplate) -> bool:
        """Validate template structure and required fields"""
        # Check meta section
        if not template.meta:
            raise ValueError("Template must have a meta section")
        
        if 'name' not in template.meta:
            raise ValueError("Template meta must have a name")
        
        # Check paragraphs
        if not template.paragraphs:
            raise ValueError("Template must have at least one paragraph")
        
        # Validate each paragraph
        for i, paragraph in enumerate(template.paragraphs):
            if not paragraph.tracks:
                raise ValueError(f"Paragraph {i} must have at least one track")
            
            # Validate tracks
            for j, track in enumerate(paragraph.tracks):
                if track.type not in ['video', 'audio', 'effect', 'image', 'text']:
                    raise ValueError(f"Invalid track type: {track.type} in paragraph {i}, track {j}")
                
                if track.timing not in ['relative', 'absolute']:
                    raise ValueError(f"Invalid timing: {track.timing} in paragraph {i}, track {j}")
                
                # Validate keyframes
                for k, keyframe in enumerate(track.keyframes):
                    if not keyframe.relative:
                        raise ValueError(f"Keyframe must have relative positioning in paragraph {i}, track {j}, keyframe {k}")
                    
                    if 'type' not in keyframe.relative:
                        raise ValueError(f"Keyframe relative must have type (pre/middle/tail) in paragraph {i}, track {j}, keyframe {k}")
                    
                    if keyframe.relative['type'] not in ['pre', 'middle', 'tail']:
                        raise ValueError(f"Invalid keyframe relative type: {keyframe.relative['type']} in paragraph {i}, track {j}, keyframe {k}")
                    
                    if 'position' not in keyframe.relative:
                        raise ValueError(f"Keyframe relative must have position (start/end) in paragraph {i}, track {j}, keyframe {k}")
                    
                    if keyframe.relative['position'] not in ['start', 'end']:
                        raise ValueError(f"Invalid keyframe relative position: {keyframe.relative['position']} in paragraph {i}, track {j}, keyframe {k}")
                    
                    # Check for index in middle type
                    if keyframe.relative['type'] == 'middle' and keyframe.relative['position'] == 'start':
                        if 'index' not in keyframe.relative:
                            # If no index specified, default to 0
                            keyframe.relative['index'] = 0
        
        return True